import AudioIcon from '@/assets/office/audio.svg';
import ErrorIcon from '@/assets/office/error.svg';
import SpinIcon from '@/assets/office/spin.svg';
import UploadIcon from '@/assets/office/uploadmeet.svg';
import VideoIcon from '@/assets/office/video.svg';
import { apiMergeChunk, apiUploadChunk, uploadApi } from '@/services/chat';
import { ConfigSelectType, ConfigType, getConfig } from '@/services/meeting';
import { getAccessToken, getTenantId, parseSizeToBytes, uupload } from '@/utils';
import { IconClose } from '@douyinfe/semi-icons';
import { Popconfirm, Select, Toast, Typography, Upload } from '@douyinfe/semi-ui';
import { customRequestArgs } from '@douyinfe/semi-ui/lib/es/upload';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

const video = '.mp4, .flv, .mov, .avi';
const autio = '.mp3, .aac, .wav, .m4a';

const getFileType = (url: string | undefined): number => {
  if (!url) return 0;
  const extension = url.split('.').pop();
  if (extension && autio.includes(extension)) {
    return 0; // 音频
  }
  if (extension && video.includes(extension)) {
    return 1; // 视频
  }
  // 默认返回音频
  return 0;
};

/**
 * 分片上传默认大小
 * @constant
 * @type {string}
 * @default
 */
const FRAGMENT_DEFAULT_SIZE = '5MB';

const UPLOAD_CONFIG = {
  url: process.env.API_URL + uploadApi,
  data: {
    path: 'ai/portal/',
    configName: 'aifile',
  },
  maxSize: 500 * 1024 * 1024,
  maxCount: 1,
  acceptTypes: video.concat(autio),
} as const;

const getUploadHeaders = () => ({
  'tenant-id': getTenantId(),
  authorization: 'Bearer ' + getAccessToken(),
});

interface Props {
  onGenerateConfig: (e: any) => void;
}
interface FileInfo {
  name: string;
  uid: string;
  size: string;
  status: string;
  url: string;
  loaded?: number; // 已上传大小
  total?: number; // 总大小
}

const GenerateConfig: React.FC<Props> = ({ onGenerateConfig }) => {
  const { Text } = Typography;
  const uploadRef = useRef<Upload>(null);
  const [fileList, setFileList] = useState<FileInfo | null>();
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [config, setConfig] = useState<ConfigType>();
  const handleLanguageSelect = (index: number) => {
    setConfig({
      languageCodes:
        config?.languageCodes?.map((item, i) => ({
          ...item,
          isSelect: i === index,
        })) || [],
      speakerCodes: config?.speakerCodes || [],
    });
  };

  const handleUploadError = useCallback((file: any) => {
    Toast.error(`上传文件"${file.name}"失败，请重试`);
  }, []);

  const handleSizeError = useCallback((file: any) => {
    return Toast.error(
      `${file.name} 超出${UPLOAD_CONFIG.maxSize / 1024 / 1024}MB大小限制请重新上传`,
    );
  }, []);

  const handleExceed = useCallback(() => {
    Toast.info(`最多只能上传${UPLOAD_CONFIG.maxCount}个文件`);
  }, []);

  /**
   * 格式化文件大小显示
   * @param bytes 字节数
   * @returns 格式化后的文件大小字符串
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 分片上传
   * @param data
   */
  async function handleCustomRequest(data: customRequestArgs) {
    const { file, fileInstance } = data;
    setOriginalFile(fileInstance);

    console.log('🚀 ~ handleCustomRequest ~ data:', file);

    const fileInfo: FileInfo = {
      name: file.name,
      uid: file.uid,
      size: file.size,
      status: 'uploading',
      url: '',
      loaded: 0,
      total: parseSizeToBytes(file.size) as number,
    };

    setFileList(fileInfo);

    // 获取分片大小
    const fragmentSize = FRAGMENT_DEFAULT_SIZE;

    // 切片大小，单位byte
    const sliceSize = parseSizeToBytes(fragmentSize) as number;

    // 文件大小
    const fileSize = parseSizeToBytes(file.size) as number;

    const totalSlices = Math.ceil(fileSize / sliceSize); // 获取切片数量

    // 如果没有分片，直接上传
    if (fileSize <= sliceSize) {
      const formData = new FormData();
      formData.append('path', UPLOAD_CONFIG.data.path);
      formData.append('configName', UPLOAD_CONFIG.data.configName);
      formData.append('file', fileInstance);

      const res = await uupload(fileInstance);

      if (res.data) {
        setFileList({
          ...fileInfo,
          status: 'done',
          url: res.data,
          loaded: fileInfo.total,
        });
      } else {
        setFileList({ ...fileInfo, status: 'error' });
      }

      return;
    }

    let chunk = 0;
    let postPromises: Promise<any>[] = []; // 存储每个 POST 请求的 Promise
    let totalUploaded = 0; // 总已上传数据量

    const onUploadProgress = (progressEvent: any, chunkIndex: number) => {
      // 可以在这里处理进度更新，但不增加 totalUploaded
      if (progressEvent.loaded === progressEvent.total) {
        // 分片上传完成的处理在 Promise.then 中进行
      }
    };

    // 分片上传
    while (chunk < totalSlices) {
      const start = chunk * sliceSize;
      const end = Math.min(start + sliceSize, fileSize);
      const slice = fileInstance.slice(start, end); // 上传的分片，通过开始结束截取
      console.log('🚀 ~ handleCustomRequest ~ slice:', slice);
      const formData = new FormData();
      // 上传接口入参
      formData.append('file', slice);
      formData.append('path', UPLOAD_CONFIG.data.path);
      formData.append('fileId', file.uid);
      formData.append('type', fileInstance.type);
      formData.append('originalFilename', file.name);
      formData.append('index', String(chunk));
      formData.append('totalChunks', String(totalSlices));
      formData.append('configName', 'aifile');
      // const apiChunk: Promise<any> = apiUploadChunk(formData, onUploadProgress);
      const apiChunk: Promise<any> = apiUploadChunk(formData, (progressEvent) =>
        onUploadProgress(progressEvent, chunk),
      )
        .then((res) => {
          if (!res.data) return;
          // 只有在上传成功时才增加计数
          totalUploaded++;
          const currentLoaded = Math.min(totalUploaded * sliceSize, fileSize);
          setFileList((prev) => {
            if (!prev) return null;
            return {
              ...prev,
              loaded: currentLoaded,
            };
          });
        })
        .catch((error) => {
          // 处理上传失败的情况
          console.error(`Chunk ${chunk} upload failed:`, error);
          throw error; // 重新抛出错误，让 Promise.all 捕获
        });
      postPromises.push(apiChunk);
      chunk++;
    }

    try {
      await Promise.all(postPromises);
      /* 合并操作 */
      const formData = new FormData();
      formData.append('path', UPLOAD_CONFIG.data.path);
      formData.append('fileId', file.uid);
      formData.append('type', fileInstance.type);
      formData.append('originalFilename', file.name);
      formData.append('totalChunks', String(totalSlices));
      formData.append('configName', 'aifile');
      const _mergeResult: any = await apiMergeChunk(formData);
      console.log('🚀 ~ handleCustomRequest ~ _mergeResult:', _mergeResult);
      if (_mergeResult.data) {
        setFileList((prev) =>
          prev
            ? {
                ...prev,
                status: 'done',
                url: _mergeResult.data.url,
                loaded: prev.total, //
              }
            : null,
        );
      } else {
        setFileList({ ...fileInfo, status: 'error' });
      }
    } catch (error) {
      setFileList((prev) => (prev ? { ...prev, status: 'error' } : null));
      console.log('🚀 ~ handleCustomRequest ~ error:', error);
    }
  }

  // 添加重新上传方法
  const handleReupload = useCallback(async () => {
    if (!originalFile || !fileList) return;

    // 创建模拟的上传参数
    const data: customRequestArgs = {
      file: {
        name: fileList.name,
        uid: fileList.uid,
        size: fileList.size,
        url: '',
        status: 'uploading',
      },
      fileInstance: originalFile,
      onError: () => {},
      fileName: '',
      data: {},
      onProgress: function (e?: { total: number; loaded: number }) {
        throw new Error('Function not implemented.');
      },
      onSuccess: function (response: any, e?: Event) {
        throw new Error('Function not implemented.');
      },
      withCredentials: false,
      action: '',
    };

    // 调用上传方法
    await handleCustomRequest(data);
  }, [originalFile, fileList]);

  const handleClearFile = useCallback(() => {
    setFileList(null);
    setOriginalFile(null);
    if (uploadRef.current) {
      // 清除上传组件中的文件列表
      uploadRef.current.clear();
    }
  }, []);

  const getPage = async () => {
    try {
      const res = await getConfig();
      if (res.data) {
        setConfig({
          languageCodes: res.data.languageCodes.map((i, k) => ({
            ...i,
            isSelect: k === 0 ? true : false,
          })),
          speakerCodes: res.data.speakerCodes.map((i) => ({
            ...i,
            isSelect: false,
          })),
        });
      }
    } catch (error) {
      console.log('getConfig=>', error);
    }
  };

  useEffect(() => {
    getPage();
  }, []);

  return (
    <div className={styles.meetingPage}>
      <div className={styles.meetingPageL}>
        <Upload
          ref={uploadRef}
          className={styles.meetingPageLUp}
          headers={getUploadHeaders()}
          fileName="file"
          action={UPLOAD_CONFIG.url}
          data={UPLOAD_CONFIG.data}
          customRequest={handleCustomRequest}
          listType="picture"
          showPicInfo
          showUploadList={false}
          limit={UPLOAD_CONFIG.maxCount}
          maxSize={UPLOAD_CONFIG.maxSize}
          accept={UPLOAD_CONFIG.acceptTypes}
          draggable
          multiple={false}
          onSizeError={handleSizeError}
          onExceed={handleExceed}
          onError={handleUploadError}
        />
        <div
          className={styles.meetingPageLC}
          style={{ pointerEvents: fileList?.status === 'error' ? 'unset' : 'none' }}
        >
          {fileList?.status ? (
            <div className={styles.meetingPageLCT}>
              <img
                src={fileList?.name?.match(/\.(mp4|flv)$/i) ? VideoIcon : AudioIcon}
                className={styles.meetingPageLCWI}
              />
              <Text
                ellipsis={{
                  rows: 1,
                  showTooltip: true,
                }}
                className={styles.meetingPageLCWN}
              >
                {fileList?.name}
              </Text>
              <div className={styles.meetingPageLCWS}>
                {fileList.loaded !== undefined && fileList.total !== undefined
                  ? `${formatFileSize(fileList.loaded)} / ${formatFileSize(fileList.total)}`
                  : ''}
              </div>
              <div className={styles.meetingPageLCTT}>
                {fileList?.status === 'uploading' ? (
                  <>
                    <img src={SpinIcon} className={styles.meetingPageLCWST} />
                  </>
                ) : fileList?.status === 'error' ? (
                  <div>
                    <img
                      src={ErrorIcon}
                      className={styles.meetingPageLCWST}
                      style={{ animation: 'none' }}
                    />
                  </div>
                ) : null}
                <span
                  className={styles.meetingPageLCWSTT}
                  style={{
                    color: fileList?.status === 'error' ? '#EC4F4F' : '#005bf8',
                  }}
                >
                  {fileList?.status === 'uploading'
                    ? '上传中'
                    : fileList?.status === 'error'
                    ? '上传失败'
                    : '上传成功'}
                </span>
                {fileList?.status === 'error' && (
                  <Popconfirm title="是否重新上传该文件?" onConfirm={handleReupload}>
                    <span
                      className={styles.meetingPageLCWSTT}
                      style={{
                        color: '#005bf8',
                        marginLeft: '4px',
                        borderBottom: '1px solid #005bf8',
                        cursor: 'pointer',
                        pointerEvents: 'auto',
                      }}
                    >
                      重新上传
                    </span>
                  </Popconfirm>
                )}
                {(fileList?.status === 'done' || fileList?.status === 'error') && (
                  <IconClose
                    className={styles.clearButton}
                    onClick={handleClearFile}
                    style={{
                      marginLeft: '8px',
                      cursor: 'pointer',
                      color: '#999',
                    }}
                  />
                )}
              </div>
            </div>
          ) : (
            <div className={styles.meetingPageLCW}>
              <img src={UploadIcon} className={styles.meetingPageLCWI} alt="上传图片" />
              <div className={styles.meetingPageLCWC}>
                <span className={styles.meetingPageLCWCL}>点击/拖拽</span>
                <span className={styles.meetingPageLCWCR}>视频或音频到此处上传</span>
              </div>
              <div className={styles.meetingPageLCWT}>
                支持 MP4，MP3，FLV，AAC，WAV，文件大小不超过500M
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={styles.meetingPageR}>
        <div className={styles.meetingPageR_title}>
          <div className={styles.meetingPageR_title_span}>音频语言</div>
          <div className={styles.meetingPageR_title_div}>
            {config?.languageCodes.map((item: ConfigSelectType, index) => (
              <div
                className={styles.meetingPageR_title_div_item}
                style={{
                  color: item.isSelect ? '#005BF8' : '#000',
                  borderColor: item.isSelect ? '#005BF8' : '#ebeef2',
                }}
                key={index}
                onClick={() => handleLanguageSelect(index)}
              >
                {item.name}
              </div>
            ))}
          </div>
        </div>
        <div className={styles.meetingPageR_title}>
          <div className={styles.meetingPageR_title_span}>说话人数</div>
          <div className={styles.meetingPageR_title_div}>
            <div
              className={styles.meetingPageR_title_div_item}
              style={{
                color: config?.speakerCodes.some((item) => item.isSelect) ? '#005BF8' : '#000',
                borderColor: config?.speakerCodes.some((item) => item.isSelect)
                  ? '#005BF8'
                  : '#ebeef2',
                paddingTop: '5px',
                paddingBottom: '5px',
              }}
              // onClick={() => setPeople({ ...people, isSelect: true })}
            >
              <Select
                className={
                  config?.speakerCodes.some((item) => item.isSelect)
                    ? styles.meetingPageR_title_div_item_select
                    : ''
                }
                defaultValue={config?.speakerCodes[0].name || '自动区分'}
                optionList={config?.speakerCodes.map((item) => {
                  return {
                    label: item.name,
                    value: item.name,
                  };
                })}
                onChange={(value) => {
                  setConfig({
                    speakerCodes: (config?.speakerCodes ?? []).map((item) => ({
                      ...item,
                      isSelect: item.name === value,
                    })),
                    languageCodes: config?.languageCodes ?? [],
                  });
                }}
              />
            </div>
          </div>
        </div>
        <div
          className={styles.meetingPageR_btn}
          style={{
            cursor: fileList?.url ? 'pointer' : 'not-allowed',
            backgroundColor: fileList?.url ? '#005bf8' : '#E4E7ED',
            color: fileList?.url ? '#fff' : '#999',
          }}
          onClick={() => {
            if (!fileList?.url) {
              return;
            }
            console.log(fileList, 'fileList');
            onGenerateConfig({
              url: fileList?.url,
              fileName: fileList?.name,
              fileSize: fileList?.size,
              // 获取 fileList?.url 后缀名 判断是 autio 还是 video
              fileType: getFileType(fileList?.url),
              language: config?.languageCodes.find((item) => item.isSelect)?.code || 'chinese',
              speakers: config?.speakerCodes.find((item) => item.isSelect)?.code ?? true,
            });
          }}
        >
          确 认
        </div>
      </div>
    </div>
  );
};

export default GenerateConfig;

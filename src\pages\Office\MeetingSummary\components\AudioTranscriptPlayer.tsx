import {
  AudioBackwardDisabledIcon,
  AudioBackwardIcon,
  AudioForwardDisabledIcon,
  AudioForwardIcon,
  AudioMutedIcon,
  AudioNotMutedIcon,
  AudioPauseIcon,
  AudioPlayIcon,
  BackTopIcon,
  EditIcon,
  MeetingAvatarIcon,
} from '@/assets/svg';
import { DialogueRecordType, updateSpeechContent } from '@/services/meeting';
import { secondsToTimestamp } from '@/utils';
import { Button, Select, Tag, TextArea, Toast, Tooltip } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { useSelector } from 'umi';
import styles from '../index.less';

export interface TranscriptSegment {
  id: string;
  start: number; // seconds
  end: number; // seconds
  text: string;
  speaker?: string;
}

export interface AudioTranscriptPlayerProps {
  audioSrc: string;
  transcript: DialogueRecordType[];
  autoPlay?: boolean;
}

const PLAYBACK_RATES = [0.5, 1, 1.5, 2] as const;
const PLAYBACK_RATE_OPTIONS = PLAYBACK_RATES.map((rate) => ({
  label: `${rate}x`,
  value: rate,
}));
type PlaybackRate = (typeof PLAYBACK_RATES)[number];

// 优化后的状态管理 - 使用 useReducer 统一管理
interface PlayerState {
  isReady: boolean;
  playing: boolean;
  muted: boolean;
  volume: number;
  playbackRate: PlaybackRate;
  played: number;
  loaded: number;
  duration: number;
  seeking: boolean;
  loadedSeconds: number;
  playedSeconds: number;
}

interface AppState {
  player: PlayerState;
  activeIndex: number;
  editingId: string | number | null;
  editingText: string;
  transcriptData: DialogueRecordType[];
  isManualClick: boolean;
}

type PlayerAction =
  | { type: 'SET_READY'; payload: boolean }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_MUTED'; payload: boolean }
  | { type: 'SET_VOLUME'; payload: number }
  | { type: 'SET_PLAYBACK_RATE'; payload: PlaybackRate }
  | { type: 'SET_PLAYED'; payload: number }
  | { type: 'SET_LOADED'; payload: number }
  | { type: 'SET_DURATION'; payload: number }
  | { type: 'SET_SEEKING'; payload: boolean }
  | { type: 'SET_LOADED_SECONDS'; payload: number }
  | { type: 'SET_PLAYED_SECONDS'; payload: number }
  | {
      type: 'UPDATE_PROGRESS';
      payload: { playedSeconds: number; played: number; loadedSeconds: number; loaded: number };
    }
  | { type: 'SET_ACTIVE_INDEX'; payload: number }
  | { type: 'START_EDIT'; payload: { id: string | number; text: string } }
  | { type: 'CANCEL_EDIT' }
  | { type: 'SET_EDITING_TEXT'; payload: string }
  | { type: 'UPDATE_TRANSCRIPT'; payload: DialogueRecordType[] }
  | { type: 'SET_MANUAL_CLICK'; payload: boolean }
  | { type: 'SEEK_TO'; payload: { time: number; duration: number } };

const initialPlayerState: PlayerState = {
  isReady: false,
  playing: false,
  muted: false,
  volume: 1,
  playbackRate: 1 as PlaybackRate,
  played: 0,
  loaded: 0,
  duration: 0,
  seeking: false,
  loadedSeconds: 0,
  playedSeconds: 0,
};

const createInitialState = (autoPlay: boolean, transcript: DialogueRecordType[]): AppState => ({
  player: {
    ...initialPlayerState,
    playing: !!autoPlay,
  },
  activeIndex: -1,
  editingId: null,
  editingText: '',
  transcriptData: transcript,
  isManualClick: false,
});

// 状态管理 reducer
const appReducer = (state: AppState, action: PlayerAction): AppState => {
  switch (action.type) {
    case 'SET_READY':
      return { ...state, player: { ...state.player, isReady: action.payload } };
    case 'SET_PLAYING':
      return { ...state, player: { ...state.player, playing: action.payload } };
    case 'SET_MUTED':
      return { ...state, player: { ...state.player, muted: action.payload } };
    case 'SET_VOLUME':
      return { ...state, player: { ...state.player, volume: action.payload } };
    case 'SET_PLAYBACK_RATE':
      return { ...state, player: { ...state.player, playbackRate: action.payload } };
    case 'SET_PLAYED':
      return { ...state, player: { ...state.player, played: action.payload } };
    case 'SET_LOADED':
      return { ...state, player: { ...state.player, loaded: action.payload } };
    case 'SET_DURATION':
      return { ...state, player: { ...state.player, duration: action.payload } };
    case 'SET_SEEKING':
      return { ...state, player: { ...state.player, seeking: action.payload } };
    case 'SET_LOADED_SECONDS':
      return { ...state, player: { ...state.player, loadedSeconds: action.payload } };
    case 'SET_PLAYED_SECONDS':
      return { ...state, player: { ...state.player, playedSeconds: action.payload } };
    case 'UPDATE_PROGRESS':
      return {
        ...state,
        player: {
          ...state.player,
          playedSeconds: action.payload.playedSeconds,
          played: action.payload.played,
          loadedSeconds: action.payload.loadedSeconds,
          loaded: action.payload.loaded,
        },
      };
    case 'SET_ACTIVE_INDEX':
      return { ...state, activeIndex: action.payload };
    case 'START_EDIT':
      return {
        ...state,
        editingId: action.payload.id,
        editingText: action.payload.text,
        player: { ...state.player, playing: false }, // 编辑时停止播放
      };
    case 'CANCEL_EDIT':
      return { ...state, editingId: null, editingText: '' };
    case 'SET_EDITING_TEXT':
      return { ...state, editingText: action.payload };
    case 'UPDATE_TRANSCRIPT':
      return { ...state, transcriptData: action.payload };
    case 'SET_MANUAL_CLICK':
      return { ...state, isManualClick: action.payload };
    case 'SEEK_TO':
      return {
        ...state,
        player: {
          ...state.player,
          played: action.payload.time / action.payload.duration,
          playing: true,
        },
      };
    default:
      return state;
  }
};

const AudioTranscriptPlayer: React.FC<AudioTranscriptPlayerProps> = ({
  audioSrc,
  transcript,
  autoPlay,
}) => {
  // 播放器和DOM引用
  const playerRef = useRef<any>(null);
  const listContainerRef = useRef<HTMLDivElement | null>(null);
  const segmentRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  // 使用 useReducer 统一管理状态
  const [state, dispatch] = useReducer(appReducer, createInitialState(!!autoPlay, transcript));

  // 辅助引用 - 优化性能和防止内存泄漏
  const pendingSeekRef = useRef<number | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = useRef<number>(0);
  const lastActiveIndexRef = useRef<number>(-1);
  const manualClickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化转录数据 - 当外部数据变化时更新
  useEffect(() => {
    dispatch({ type: 'UPDATE_TRANSCRIPT', payload: transcript });
  }, [transcript]);

  // 按时间排序的转录片段（缓存计算结果）- 优化大数据量性能
  const orderedTranscript = useMemo(() => {
    return [...state.transcriptData].sort((a, b) => a.startTime - b.startTime);
  }, [state.transcriptData]);

  // 确保初始渲染正常 - 修复白屏问题
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // 确保组件完全初始化后再显示内容
    if (orderedTranscript.length > 0) {
      requestAnimationFrame(() => {
        setIsInitialized(true);
      });
    }
  }, [orderedTranscript.length]);

  // 简化渲染逻辑 - 移除虚拟滚动避免复杂问题
  // 直接渲染所有项目，确保稳定性和可靠性

  // 1. 查找当前时间对应的活跃片段索引
  const findActiveIndex = useCallback(
    (time: number): number => {
      if (!orderedTranscript.length) return -1;
      for (let i = 0; i < orderedTranscript.length; i++) {
        const seg = orderedTranscript[i];
        if (time >= seg.startTime && time < seg.endTime) return i; // 落在当前片段内
        if (time < seg.startTime) return Math.max(0, i - 1); // 落在前一个片段后
      }
      return orderedTranscript.length - 1; // 超出最后一个片段
    },
    [orderedTranscript],
  );

  // 2. 滚动到活跃片段（防抖处理）- 优化版本
  const scrollToActive = useCallback(
    (index: number, force: boolean = false) => {
      if (index < 0 || index >= orderedTranscript.length) return;
      if (!force && index === lastActiveIndexRef.current) return;

      const seg = orderedTranscript[index];
      const node = segmentRefs.current.get(String(seg.id));
      const container = listContainerRef.current;
      if (!node || !container) return;

      // 清理之前的滚动定时器
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      const performScroll = () => {
        try {
          const nodeTop = node.offsetTop;
          const nodeHeight = node.offsetHeight;
          const containerHeight = container.clientHeight;

          // 确保节点在可视区域内，但不要过度居中
          const currentScrollTop = container.scrollTop;
          const nodeBottom = nodeTop + nodeHeight;
          const visibleTop = currentScrollTop;
          const visibleBottom = currentScrollTop + containerHeight;

          let targetScrollTop = currentScrollTop;

          // 如果节点在可视区域上方
          if (nodeTop < visibleTop) {
            targetScrollTop = Math.max(0, nodeTop - 50); // 留50px边距
          }
          // 如果节点在可视区域下方
          else if (nodeBottom > visibleBottom) {
            targetScrollTop = Math.max(0, nodeBottom - containerHeight + 50); // 留50px边距
          }

          if (Math.abs(targetScrollTop - currentScrollTop) > 10) {
            // 只有差距大于10px才滚动
            container.scrollTo({
              top: targetScrollTop,
              behavior: force ? 'auto' : 'smooth',
            });
          }

          lastScrollTimeRef.current = Date.now();
          lastActiveIndexRef.current = index;
        } catch (error) {
          console.warn('滚动出错:', error);
        }
      };

      if (force) {
        performScroll();
      } else {
        scrollTimeoutRef.current = setTimeout(performScroll, 100);
      }
    },
    [orderedTranscript],
  );

  // 3. 自动更新活跃索引（节流控制滚动）
  useEffect(() => {
    // 如果是手动点击，暂时不自动更新activeIndex
    if (state.isManualClick) return;

    const nextIndex = findActiveIndex(state.player.playedSeconds);
    if (nextIndex !== state.activeIndex) {
      dispatch({ type: 'SET_ACTIVE_INDEX', payload: nextIndex });
      const now = Date.now();
      if (now - lastScrollTimeRef.current >= 500) {
        scrollToActive(nextIndex);
      }
    }
  }, [
    state.player.playedSeconds,
    state.activeIndex,
    state.isManualClick,
    findActiveIndex,
    scrollToActive,
  ]);

  // 清理定时器（避免内存泄漏）- 优化后的版本
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
      if (manualClickTimeoutRef.current) clearTimeout(manualClickTimeoutRef.current);
    };
  }, []);

  // 4. 安全跳转播放时间
  const safeSeek = useCallback((seconds: number) => {
    const player = playerRef.current;
    if (player?.seekTo) {
      player.seekTo(seconds, 'seconds');
      return;
    }
    const internal = player?.getInternalPlayer?.();
    if (internal && typeof (internal as any).currentTime === 'number') {
      (internal as any).currentTime = seconds;
      return;
    }
    pendingSeekRef.current = seconds; // 播放器未就绪时缓存跳转
  }, []);

  // 5. 进度条控制 - 优化后的版本
  const handleSeekMouseDown = useCallback(() => {
    dispatch({ type: 'SET_SEEKING', payload: true });
  }, []);

  const handleSeekChange = useCallback((event: React.SyntheticEvent<HTMLInputElement>) => {
    const played = Number.parseFloat((event.target as HTMLInputElement).value);
    dispatch({ type: 'SET_PLAYED', payload: played });
  }, []);

  const handleSeekMouseUp = useCallback(
    (event: React.SyntheticEvent<HTMLInputElement>) => {
      const played = Number.parseFloat((event.target as HTMLInputElement).value);
      dispatch({ type: 'SET_SEEKING', payload: false });
      if (playerRef.current && state.player.duration) {
        playerRef.current.currentTime = played * state.player.duration;
      }
    },
    [state.player.duration],
  );

  const handleReady = useCallback(() => {
    console.log('handleReady');
    dispatch({ type: 'SET_READY', payload: true });

    // 处理待处理的跳转
    if (pendingSeekRef.current !== null) {
      safeSeek(pendingSeekRef.current);
      pendingSeekRef.current = null;
    }
  }, []);

  // 6. 播放状态控制 - 优化后的版本
  const handlePlay = useCallback(() => {
    dispatch({ type: 'SET_PLAYING', payload: true });
  }, []);

  const handlePause = useCallback(() => {
    dispatch({ type: 'SET_PLAYING', payload: false });
  }, []);

  const handleEnded = useCallback(() => {
    dispatch({ type: 'SET_PLAYING', payload: false });
  }, []);

  // 7. 播放器事件处理 - 优化后的版本
  const handleDurationChange = useCallback(() => {
    const player = playerRef.current;
    if (player) {
      dispatch({ type: 'SET_DURATION', payload: player.duration });
    }
  }, []);

  const handleProgress = useCallback(() => {
    const player = playerRef.current;
    if (!player || state.player.seeking || !player.buffered?.length) return;

    const loadedSeconds = player.buffered.end(player.buffered.length - 1);
    const loaded = loadedSeconds / player.duration;

    dispatch({ type: 'SET_LOADED_SECONDS', payload: loadedSeconds });
    dispatch({ type: 'SET_LOADED', payload: loaded });
  }, [state.player.seeking]);

  const handleRateChange = useCallback(() => {
    const player = playerRef.current;
    if (player) {
      dispatch({ type: 'SET_PLAYBACK_RATE', payload: player.playbackRate });
    }
  }, []);

  const handleTimeUpdate = useCallback(() => {
    const player = playerRef.current;
    if (!player || state.player.seeking || !player.duration) return;

    const playedSeconds = player.currentTime;
    const played = playedSeconds / player.duration;

    // 使用批量更新优化性能
    dispatch({
      type: 'UPDATE_PROGRESS',
      payload: {
        playedSeconds,
        played,
        loadedSeconds: state.player.loadedSeconds,
        loaded: state.player.loaded,
      },
    });
  }, [state.player.seeking, state.player.loadedSeconds, state.player.loaded]);

  // 8. 音量和播放速率控制 - 优化后的版本
  const handleVolumeChange = useCallback((event: React.SyntheticEvent<HTMLInputElement>) => {
    const volume = Number.parseFloat((event.target as HTMLInputElement).value);
    dispatch({ type: 'SET_VOLUME', payload: volume });
  }, []);

  const handleToggleMuted = useCallback(() => {
    dispatch({ type: 'SET_MUTED', payload: !state.player.muted });
  }, [state.player.muted]);

  const handleSetPlaybackRate = useCallback((rate: PlaybackRate) => {
    dispatch({ type: 'SET_PLAYBACK_RATE', payload: rate });
  }, []);

  // 9. 点击片段跳转播放 - 修复点击无反应问题
  const handleItemClick = useCallback(
    (index: number) => {
      console.log('点击项目:', index, orderedTranscript[index]);

      const seg = orderedTranscript[index];
      if (!seg) {
        console.warn('未找到片段:', index);
        return;
      }

      try {
        // 设置手动点击状态
        dispatch({ type: 'SET_MANUAL_CLICK', payload: true });

        // 清除自动滚动
        if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

        // 立即设置活跃索引
        dispatch({ type: 'SET_ACTIVE_INDEX', payload: index });

        // 跳转并播放
        if (playerRef.current) {
          const player = playerRef.current;

          // 直接设置时间
          if (player.seekTo) {
            player.seekTo(seg.startTime, 'seconds');
          } else if (player.currentTime !== undefined) {
            player.currentTime = seg.startTime;
          }

          // 更新状态
          if (state.player.duration > 0) {
            dispatch({
              type: 'SEEK_TO',
              payload: { time: seg.startTime, duration: state.player.duration },
            });
          } else {
            // 如果duration还没有加载，只设置播放状态
            dispatch({ type: 'SET_PLAYING', payload: true });
          }
        }

        // 滚动到对应位置（延迟执行确保DOM更新）
        requestAnimationFrame(() => {
          scrollToActive(index, true);
        });

        // 清理手动点击状态
        if (manualClickTimeoutRef.current) clearTimeout(manualClickTimeoutRef.current);
        manualClickTimeoutRef.current = setTimeout(() => {
          dispatch({ type: 'SET_MANUAL_CLICK', payload: false });
        }, 1000); // 增加到1秒确保稳定
      } catch (error) {
        console.error('点击处理出错:', error);
        // 确保状态被重置
        dispatch({ type: 'SET_MANUAL_CLICK', payload: false });
      }
    },
    [orderedTranscript, state.player.duration, scrollToActive],
  );

  // 10. 上一个/下一个对话核心逻辑（按顺序导航，允许同一说话人）- 优化后的版本
  const getPrevDialog = useCallback((): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0) return null;
    // 初始状态（activeIndex=-1）或第一个片段，返回null
    if (state.activeIndex <= 0) return null;
    const prevIndex = state.activeIndex - 1;
    const prevSeg = orderedTranscript[prevIndex];
    return { time: prevSeg.startTime, index: prevIndex };
  }, [orderedTranscript, state.activeIndex]);

  const getNextDialog = useCallback((): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0) return null;
    const lastIndex = orderedTranscript.length - 1;
    // 初始状态（activeIndex=-1）时，返回第一个片段
    if (state.activeIndex === -1) return { time: orderedTranscript[0].startTime, index: 0 };
    // 最后一个片段，返回null
    if (state.activeIndex >= lastIndex) return null;
    const nextIndex = state.activeIndex + 1;
    const nextSeg = orderedTranscript[nextIndex];
    return { time: nextSeg.startTime, index: nextIndex };
  }, [orderedTranscript, state.activeIndex]);

  // 上一个对话处理 - 优化后的版本
  const handlePrevDialog = useCallback(() => {
    const result = getPrevDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      if (playerRef.current && state.player.duration) {
        playerRef.current.currentTime = time;
        dispatch({
          type: 'SEEK_TO',
          payload: { time, duration: state.player.duration },
        });
      }
      scrollToActive(index, true);
      dispatch({ type: 'SET_ACTIVE_INDEX', payload: index });
    }
  }, [getPrevDialog, state.player.duration, scrollToActive]);

  // 下一个对话处理 - 优化后的版本
  const handleNextDialog = useCallback(() => {
    const result = getNextDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      if (playerRef.current && state.player.duration) {
        playerRef.current.currentTime = time;
        dispatch({
          type: 'SEEK_TO',
          payload: { time, duration: state.player.duration },
        });
      }
      scrollToActive(index, true);
      dispatch({ type: 'SET_ACTIVE_INDEX', payload: index });
    }
  }, [getNextDialog, state.player.duration, scrollToActive]);

  // 编辑相关功能 - 优化后的版本
  const startEdit = useCallback(async (segId: string | number, initial: string) => {
    dispatch({
      type: 'START_EDIT',
      payload: { id: segId, text: initial },
    });
    // 音频停止播放
    if (playerRef.current) {
      playerRef.current.pause();
    }
  }, []);

  const cancelEdit = useCallback(() => {
    dispatch({ type: 'CANCEL_EDIT' });
  }, []);

  const handleEditKeyDown = useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      } else {
        // 普通回车，阻止默认换行行为并发送内容
        event.preventDefault();
        console.log('保存编辑');
        saveEdit();
      }
    }
  }, []);

  const saveEdit = useCallback(async () => {
    try {
      const p = {
        id: state.editingId,
        speechContent: state.editingText,
      };
      const res = await updateSpeechContent(p);
      if (res.data) {
        // 更新转录数据
        const updatedTranscript = state.transcriptData.map((s) =>
          s.id === state.editingId ? { ...s, speechContent: state.editingText } : s,
        );
        dispatch({ type: 'UPDATE_TRANSCRIPT', payload: updatedTranscript });
        dispatch({ type: 'CANCEL_EDIT' });
        Toast.success('修改成功！');
      }
    } catch (e) {
      Toast.error('修改失败！');
    }
  }, [state.editingId, state.editingText, state.transcriptData]);

  // 12. 滚动控制（优化版本）
  const scrollToTop = useCallback(() => {
    const container = listContainerRef.current;
    if (container) {
      try {
        container.scrollTo({ top: 0, behavior: 'smooth' });
      } catch (error) {
        console.warn('滚动到顶部出错:', error);
        // 降级处理
        container.scrollTop = 0;
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    const container = listContainerRef.current;
    if (container) {
      try {
        // 确保获取最新的scrollHeight
        requestAnimationFrame(() => {
          if (container) {
            container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
          }
        });
      } catch (error) {
        console.warn('滚动到底部出错:', error);
        // 降级处理
        container.scrollTop = container.scrollHeight;
      }
    }
  }, []);

  // 修复闪烁问题的展开组件
  const OptimizedParagraph = React.memo(
    ({ content, isActive }: { content: string; isActive: boolean }) => {
      const [isExpanded, setIsExpanded] = useState(false);
      const [shouldShowEllipsis, setShouldShowEllipsis] = useState(false);
      const [isCalculating, setIsCalculating] = useState(true);
      const textRef = useRef<HTMLDivElement>(null);
      const measureRef = useRef<HTMLDivElement>(null);

      // 使用隐藏的测量元素来避免闪烁
      useEffect(() => {
        if (measureRef.current && content) {
          setIsCalculating(true);

          // 使用 requestAnimationFrame 确保DOM更新完成
          requestAnimationFrame(() => {
            if (measureRef.current) {
              const lineHeight = 24;
              const maxLines = 5;
              const maxHeight = lineHeight * maxLines;
              const actualHeight = measureRef.current.scrollHeight;

              setShouldShowEllipsis(actualHeight > maxHeight);
              setIsCalculating(false);
            }
          });
        }
      }, [content]);

      // 重置展开状态当内容变化时
      useEffect(() => {
        setIsExpanded(false);
      }, [content]);

      if (isCalculating) {
        return (
          <div className={styles.transcriptText}>
            <div style={{ lineHeight: '24px', minHeight: '24px' }}>{content}</div>
            {/* 隐藏的测量元素 */}
            <div
              ref={measureRef}
              style={{
                position: 'absolute',
                visibility: 'hidden',
                height: 'auto',
                width: '100%',
                lineHeight: '24px',
                pointerEvents: 'none',
              }}
            >
              {content}
            </div>
          </div>
        );
      }

      return (
        <div className={styles.transcriptText}>
          <div
            ref={textRef}
            style={{
              maxHeight: isExpanded ? 'none' : '120px',
              overflow: 'hidden',
              lineHeight: '24px',
              transition: 'max-height 0.2s ease-in-out',
            }}
          >
            {content}
          </div>
          {shouldShowEllipsis && (
            <Button
              theme="borderless"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
              style={{
                padding: '4px 0',
                marginTop: '4px',
                fontSize: '12px',
                opacity: isCalculating ? 0 : 1,
                transition: 'opacity 0.2s ease-in-out',
              }}
            >
              {isExpanded ? '收起' : '展开全部'}
            </Button>
          )}
        </div>
      );
    },
  );

  const RateSelectRender = (props: any) => {
    const { value } = props;
    const displayValue = Array.isArray(value)
      ? value.map((item: any) => (typeof item === 'object' ? item.label : item)).join(' / ')
      : value;

    return (
      <div className={styles.rateSelect}>
        <Tag size="large" color="white" shape="circle">
          {displayValue}
        </Tag>
      </div>
    );
  };

  return (
    <div className={styles.meetingSummaryWrapper}>
      {/* 对话列表 */}
      <div
        className={classNames(styles.transcriptContainer, {
          [styles.transcriptContainerWrap]: ['meeting'].indexOf(pageMode) > -1,
        })}
        ref={listContainerRef}
      >
        {/* 确保初始化完成后再渲染内容 */}
        {!isInitialized && orderedTranscript.length > 0 && (
          <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>加载中...</div>
        )}

        {/* 简化渲染 - 直接渲染所有项目 */}
        {isInitialized &&
          orderedTranscript.map((seg, index) => {
            const isActive = index === state.activeIndex;
            const isEditing = seg.id === state.editingId;
            return (
              <div
                key={seg.id}
                ref={(el) => el && segmentRefs.current.set(String(seg.id), el)}
                className={`${styles.transcriptItem} ${
                  isActive ? styles.transcriptItemActive : ''
                }`}
                onClick={() => !isEditing && handleItemClick(index)}
              >
                <div className={styles.transcriptMeta}>
                  {/* <img src="/images/avatar.png" alt="" className={styles.transcriptAvatar} /> */}
                  <MeetingAvatarIcon />
                  {seg.personName && (
                    <span className={styles.transcriptSpeaker}>{seg.personName}</span>
                  )}
                  <span className={styles.transcriptTime}>{seg.startTimeStr}</span>
                  {!isEditing ? (
                    <Button
                      className={styles.editButton}
                      icon={<EditIcon />}
                      theme="borderless"
                      onClick={(e) => {
                        e.stopPropagation();
                        startEdit(seg.id, seg.speechContent);
                      }}
                    />
                  ) : (
                    <div className={styles.saveAction}>
                      <Button
                        className={styles.editButton}
                        theme="borderless"
                        type="primary"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          saveEdit();
                        }}
                      >
                        保存
                      </Button>
                      <Button
                        className={styles.editButton}
                        theme="borderless"
                        type="tertiary"
                        onClick={(e) => {
                          e.stopPropagation();
                          cancelEdit();
                        }}
                      >
                        取消
                      </Button>
                    </div>
                  )}
                </div>
                {!isEditing ? (
                  <OptimizedParagraph content={seg.speechContent} isActive={isActive} />
                ) : (
                  <TextArea
                    className={styles.editTextarea}
                    autosize
                    value={state.editingText}
                    onChange={(value) => dispatch({ type: 'SET_EDITING_TEXT', payload: value })}
                    onKeyDown={handleEditKeyDown}
                    rows={3}
                    onClick={(e) => e.stopPropagation()}
                  />
                )}
              </div>
            );
          })}
      </div>

      {/* 音频控制栏 */}
      <div className={styles.audioBar}>
        <div className={styles.audioControls}>
          <div className={styles.controlGroup}>
            <Button
              icon={state.activeIndex <= 0 ? <AudioForwardDisabledIcon /> : <AudioForwardIcon />}
              aria-label="上一个对话"
              theme="borderless"
              disabled={state.activeIndex <= 0}
              onClick={handlePrevDialog}
            />
            <Button
              icon={state.player.playing ? <AudioPauseIcon /> : <AudioPlayIcon />}
              aria-label={state.player.playing ? '暂停' : '播放'}
              theme="borderless"
              className={styles.playButton}
              onClick={() => dispatch({ type: 'SET_PLAYING', payload: !state.player.playing })}
            />
            <Button
              icon={
                state.activeIndex >= orderedTranscript.length - 1 ? (
                  <AudioBackwardDisabledIcon />
                ) : (
                  <AudioBackwardIcon />
                )
              }
              aria-label="下一个对话"
              theme="borderless"
              onClick={handleNextDialog}
              disabled={state.activeIndex >= orderedTranscript.length - 1}
            />
          </div>
          <input
            type="range"
            min={0}
            max={1}
            step="any"
            value={state.player.played}
            // 动态设置背景渐变
            style={{
              background: `linear-gradient(
                to right,
                #005BF8 ${state.player.played * 100}%,
                #ebeef2 ${state.player.played * 100}%
              )`,
            }}
            onMouseDown={handleSeekMouseDown}
            onChange={handleSeekChange}
            onMouseUp={handleSeekMouseUp}
            className={styles.progressSlider}
          />
          <div className={styles.timeInfo}>
            <span>{secondsToTimestamp(state.player.playedSeconds)}</span>
            <span> / </span>
            <span>{secondsToTimestamp(state.player.duration)}</span>
          </div>

          <Tooltip content={state.player.muted ? '已静音' : '已开启'}>
            <Button className={styles.audioButton} theme="borderless" onClick={handleToggleMuted}>
              {state.player.muted ? <AudioNotMutedIcon /> : <AudioMutedIcon />}
            </Button>
          </Tooltip>

          <Select
            className={styles.playbackRateSelect}
            value={state.player.playbackRate}
            onChange={(value) => handleSetPlaybackRate(value as PlaybackRate)}
            triggerRender={RateSelectRender}
            optionList={PLAYBACK_RATE_OPTIONS}
          ></Select>
        </div>

        {/* 音频播放器 */}
        <ReactPlayer
          ref={playerRef}
          src={audioSrc}
          playing={state.player.playing}
          muted={state.player.muted}
          volume={state.player.volume}
          playbackRate={state.player.playbackRate}
          height={0}
          width={0}
          onReady={handleReady}
          onPlay={handlePlay}
          onPause={handlePause}
          onEnded={handleEnded}
          onDurationChange={handleDurationChange}
          onProgress={handleProgress}
          onTimeUpdate={handleTimeUpdate}
          onRateChange={handleRateChange}
          config={
            {
              file: { forceAudio: true, attributes: { preload: 'metadata' } },
            } as any
          }
        />
      </div>

      {/* 滚动控制按钮 */}
      <div className={styles.scrollControls}>
        <BackTopIcon className={styles.scrollIcon} onClick={scrollToTop} />
        <BackTopIcon
          className={[`${styles.scrollIcon} ${styles.scrollIconBottom}`]}
          onClick={scrollToBottom}
        />
      </div>
    </div>
  );
};

export default AudioTranscriptPlayer;
